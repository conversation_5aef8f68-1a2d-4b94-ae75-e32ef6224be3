{"absolute_value": 0, "align_labels_right": 0, "creation": "2025-06-16 16:19:50.008117", "css": ".print-format .letter-head {\n    display: none;\n}\n\n.print-format td, .print-format th {\n    vertical-align: top !important;\n    padding: 1px 6px !important;\n    white-space: nowrap;\n}\n\n.print-format th {\n    color: black !important;\n    font-weight: normal;\n    border-bottom-width: 1px !important;\n}\n\n.print-format {\n    padding: 20px !important;\n}\n\nhtml, body {\n    height: auto !important;\n    overflow: hidden;\n}\n\n.print-format {\n    display: block;\n    height: auto;\n    overflow: visible;\n}\n\n.print-format p {\n    margin: 0px 0px 3px !important;\n}\n\n/* Remove default ERPNext spacing */\n.frappe-control, .form-section {\n    margin-bottom: 0 !important;\n}\n\n/*.print-format img {*/\n/*    max-width: 18% !important;*/\n/*}*/\n\n\n", "custom_format": 0, "default_print_language": "en", "disabled": 0, "doc_type": "Sales Invoice", "docstatus": 0, "doctype": "Print Format", "font_size": 14, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<!-- <PERSON><PERSON> and <PERSON><PERSON> -->\\n    <div style=\\\"display: -webkit-box; -webkit-box-pack: justify; align-items: center; margin-bottom: 8px; padding: 0 6px\\\">\\n        <div>\\n            <div style=\\\"font-size: 15px; margin-top: 48px;\\\"><strong>TAX INVOICE</strong></div>\\n        </div>\\n        <div class='text-right'><img src=\\\"https://erp.rubis.co.tz/files/RTS%20Logo%20Excel.png\\\" style=\\\"height: 60px;\\\"></div>\\n    </div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<style>\\n    \\n    /* ---------- vendor table ---------- */\\n    .vendor-table td { padding: 2px 0; font-size: 11px; }\\n</style>\\n\\n{% set customer = frappe.get_doc('Customer', doc.customer) %}\\n{% set company = frappe.get_doc('Company', doc.company) %}\\n\\n        <!-- \\u25b8 Vendor details -->\\n        <table class=\\\"vendor-table\\\" style=\\\"width:100%; margin-top:10px;\\\">\\n            <tr>\\n                <td>\\n                    <b>Customer Details</b>\\n                </td>\\n                <td style='padding:5px 12rem 5px 2px !important;'></td>\\n                <td style=\\\"text-align:left; padding-left: 100px !important; font-weight: bold\\\">INVOICE NO</td>\\n                <td style=\\\"text-align:right; font-weight: bold\\\">{{ doc.name or '' }}</td>\\n            </tr>\\n            \\n            <tr>\\n                <td>{{ doc.customer_name }}</td>\\n                <td style='padding:5px 12rem 5px 2px !important;'></td>\\n                <td style=\\\"text-align:left; padding-left: 100px !important;\\\">Invoice Date</td>\\n                <td style=\\\"text-align:right;\\\">{{ frappe.utils.formatdate(doc.posting_date, \\\"dd-MMM-YYYY\\\") }}</td>\\n            </tr>\\n            \\n            <tr>\\n                <td>\\n                    {{ doc.address_display or '' }}\\n                </td>\\n                <td style='padding:5px 0 5px 2px !important;'></td>\\n                \\n            </tr>\\n            \\n            <tr>\\n                <td>TIN NO: <span>{{ doc.tax_id or '' }}</span></td>\\n                <td style='padding:5px 12rem 5px 2px !important;'></td>\\n                <td style=\\\"text-align:left; padding-left: 100px !important;\\\">TIN NO</td>\\n                <td style=\\\"text-align:right;\\\">{{ company.tin or '' }}</td>\\n            </tr>\\n            \\n            <tr>\\n                <td>VRN NO: <span>{{ doc.customer.vrn or '' }}</span></td>\\n                <td style='padding:5px 12rem 5px 2px !important;'></td>\\n                <td style=\\\"text-align:left; padding-left: 100px !important;\\\">VRN NO</td>\\n                <td style=\\\"text-align:right;\\\">{{ company.vrn or '' }}</td>\\n            </tr>\\n        </table>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<style>\\n    .main-tables {\\n        width: 100%;\\n        border-collapse: collapse;\\n        border: 1px solid #000;\\n        margin-top: 10px;\\n        font-size: 11px;\\n    }\\n    .main-tables th {\\n        border: 1px solid #000;\\n        white-space: nowrap;\\n        text-align: center;\\n    }\\n    .main-tables td {\\n        border: 1px solid #000;\\n        text-align: center;\\n    }\\n</style>\\n\\n<table class='main-tables'>\\n    <thead>\\n        <tr>\\n            <th>Customer NO</th>\\n            <th>Customer PO</th>\\n            <th>Due Date</th>\\n            <th>Project NO</th>\\n            <th>Payment Terms</th>\\n            <th>Currency</th>\\n        </tr>\\n    </thead>\\n    <tbody>\\n        <tr>\\n            <td>{{ doc.customer or '' }}</td>\\n            <td>{{ doc.po_no or '' }}</td>\\n            <td>{{ doc.due_date or '' }}</td>\\n            <td>{{ doc.project or '' }}</td>\\n            {% for term in doc.payment_schedule %}\\n            <td>{{ term.payment_term or '' }}</td>\\n            {% endfor %}\\n            <td>{{ doc.currency or ''}}</td>\\n        </tr>\\n    </tbody>\\n</table>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<!-- ===================== PURCHASE ORDER PRINT FORMAT ===================== -->\\n<style>\\n    /* ---------- fonts ---------- */\\n    @font-face {\\n        font-family: \\\"Calibri\\\";\\n        src: local(\\\"Calibri\\\"), local(\\\"Calibri-Regular\\\");\\n    }\\n\\n    /* ---------- global defaults ---------- */\\n    body       { font-family: \\\"Calibri\\\", sans-serif; font-size: 11px; color: #000; }\\n\\n    /* ---------- scaffold ---------- */\\n    .print-page {\\n        position: relative;\\n        min-height: 39vh;          \\n        box-sizing: border-box;\\n    }\\n    /* ---------- items table ---------- */\\n    .main-table {\\n        width: 100%;\\n        border-collapse: collapse;\\n        margin-top: 20px;\\n        font-size: 11px;\\n    }\\n    .main-table th {\\n        border-top: 1px solid #000;\\n        border-bottom: 1px solid #000;\\n        white-space: nowrap;\\n    }\\n\\n    @media print { @page { margin: 12mm 10mm; } .print-page { min-height: 48vh; } }\\n</style>\\n\\n<div class=\\\"print-page\\\">\\n\\n    <!-- ======== VENDOR + ITEM LIST ======== -->\\n    <div class=\\\"content-block\\\">\\n        \\n\\n        <!-- \\u25b8 Items table -->\\n        <table class=\\\"main-table\\\">\\n            <thead>\\n                <tr>\\n                    <th><strong>Item Code</strong></th>\\n                    <th><strong>Description</strong></th>\\n                    <th style='text-align:center !important;'><strong>Qty</strong></th>\\n                    <th class='text-center'><strong>Unit</strong></th>\\n                    <th class='text-right'><strong>Unit Price</strong></th>\\n                    <th class='text-right'><strong>Tax</strong></th>\\n                    <th class='text-right'><strong>Total</strong></th>\\n                </tr>\\n            </thead>\\n            <tbody>\\n                {% for item in doc.items %}\\n                    \\n                    \\n                    {% set tax_details = frappe.get_all(\\n                        \\\"Item Tax Template Detail\\\",\\n                        filters={\\\"parent\\\": item.item_tax_template},\\n                        fields=[\\\"tax_rate\\\"],\\n                        limit=1\\n                    ) %}\\n                    {% set tax_rate = tax_details[0].tax_rate if tax_details else 0 %}\\n\\n                    <tr>\\n                        <td>{{ item.item_code }}</td>\\n                        <td>{{ item.item_name }}</td>\\n                        <td class='text-center'>{{ item.qty | int }}</td>\\n                        <td class='text-center'>{{ item.uom }}</td>\\n                        <td class='text-right'>\\n                            {{ frappe.format(item.rate) }}\\n                        </td>\\n                        <td class='text-right'>\\n                            {{ frappe.format(item.amount * (tax_rate/100)) }}\\n                        </td>\\n                        <td class='text-right'>\\n                            {{ frappe.format(item.amount) }}\\n                        </td>\\n                    </tr>\\n                {% endfor %}\\n            </tbody>\\n        </table>\\n    </div>\\n</div>\\n\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<style>\\n    .terms-section   { flex: 0 0 28%; font-size: 11px; }\\n    .section-title { font-weight: bold; margin-top: 0 }\\n    \\n</style>\\n\\n<div class='d-2'>\\n    <div class=\\\"terms-section\\\">\\n        <div class=\\\"section-title\\\">Terms and Conditions</div>\\n        <p>{{ doc.tc_name or '' }}</p>\\n    </div>\\n</div>\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<style>\\n    .d-2 {\\n        position: relative;\\n        top: 208;\\n    }\\n    \\n    @media print {\\n      .d-2 {\\n        position: relative;\\n        top: 25px;\\n      }\\n    }\\n    \\n    .totals-table {\\n        width: 100%;                      /* fills right-hand column */\\n        border-collapse: collapse;\\n        font-size: 11px;\\n    }\\n    .totals-table td {\\n        padding: 2px 6px;\\n        text-align: right;\\n    }\\n    .totals-table tr td:first-child { font-weight: bold; text-align: left; }\\n</style>\\n\\n<div class='d-2 mb-3' style='padding-left: 112px !important;'>\\n    <table class=\\\"totals-table\\\">\\n        <tr><td>Subtotal:</td>\\n            <td>{{ frappe.format(doc.net_total) }}</td></tr>\\n        <tr><td>VAT 18%</td>\\n            <td>{{ frappe.format(doc.total_taxes_and_charges, \\\"Float\\\") }}</td></tr>\\n        <tr><td>Total:</td>\\n            <td>{{ frappe.format(doc.grand_total) }}</td></tr>\\n    </table>\\n</div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"{% set company = frappe.get_doc('Company', doc.company) %}\\n<div class='d-2' style='font-size: 11px; margin-top: 2px'>\\n    <div style='font-weight: bold;'>Bank Details</div>\\n    <p>{{company.company_bank_details}}</p>\\n</div>\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<style>\\n    .values-wrapper {\\n        display: flex;\\n        align-items: flex-start;\\n        padding-left: 35px;\\n    }\\n\\n    .values-wrapper.no-qr {\\n        padding-left: 112px; /* adjust for no-QR alignment */\\n    }\\n\\n    .qr-code {\\n        max-width: 80px !important;\\n        height: auto;\\n        margin-right: 0px; /* small space between QR and values */\\n    }\\n\\n    .value-table {\\n        font-size: 11px;\\n        border-collapse: collapse;\\n        width: 100%;\\n    }\\n\\n    .value-table td {\\n        padding: 2px 5px !important;\\n    }\\n\\n    .section-title {\\n        font-weight: bold;\\n        font-size: 12px;\\n        margin-bottom: 4px;\\n    }\\n</style>\\n\\n{% set has_qr = 1 if doc.vfd_verification_url else 0 %}\\n\\n<div class=\\\"values-wrapper {% if not has_qr %}no-qr{% endif %} d-2\\\">\\n    {% if has_qr %}\\n        {% set qr1 = generate_qrcode(doc.vfd_verification_url) %}\\n        <img class=\\\"qr-code\\\" src=\\\"{{ qr1 }}\\\">\\n    {% endif %}\\n\\n    <div style=\\\"flex: 1;\\\">\\n        <div class=\\\"section-title text-center\\\">Values in TZS for Tax Purpose Only</div>\\n        <table class=\\\"value-table\\\">\\n            <tr>\\n                <td>Net Amount</td>\\n                <td style=\\\"text-align:right;\\\">\\n                    {{ frappe.format(doc.base_net_total, {'fieldtype':'Currency'}) }}\\n                </td>\\n            </tr>\\n            <tr>\\n                <td>VAT 18%</td>\\n                <td style=\\\"text-align:right;\\\">\\n                    {{ frappe.format(doc.base_total_taxes_and_charges, {'fieldtype':'Currency'}) }}\\n                </td>\\n            </tr>\\n            <tr>\\n                <td>Grand Total</td>\\n                <td style=\\\"text-align:right;\\\">\\n                    {{ frappe.format(doc.base_grand_total, {'fieldtype':'Currency'}) }}\\n                </td>\\n            </tr>\\n        </table>\\n    </div>\\n</div>\\n\"}]", "idx": 0, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2025-08-14 08:57:35.575930", "modified_by": "<EMAIL>", "module": "CSF TZ", "name": "Rubis Invoice", "owner": "<EMAIL>", "page_number": "<PERSON>de", "pdf_generator": "chrome", "print_designer": 0, "print_designer_template_app": "print_designer", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}