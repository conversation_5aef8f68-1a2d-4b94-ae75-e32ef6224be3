{"absolute_value": 0, "align_labels_right": 0, "creation": "2025-06-14 17:56:56.157881", "css": ".print-format .letter-head {\n    display: none;\n}\n\n.print-format td, .print-format th {\n    vertical-align: top !important;\n    padding: 1px 6px !important;\n    white-space: nowrap;\n}\n\n.print-format th {\n    color: black !important;\n    font-weight: normal;\n    border-bottom-width: 1px !important;\n}\n\n.print-format {\n    padding: 12px !important;\n}\n\nhtml, body {\n    height: auto !important;\n    overflow: hidden;\n}\n\n.print-format {\n    display: block;\n    height: auto;\n    overflow: visible;\n}\n\n.print-format p {\n    margin: 0px 0px 3px !important;\n}\n\n/* Remove default ERPNext spacing */\n.frappe-control, .form-section {\n    margin-bottom: 0 !important;\n}\n\n\n", "custom_format": 0, "default_print_language": "en", "disabled": 0, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype": "Print Format", "font_size": 14, "format_data": "[{\"fieldname\": \"print_heading_template\", \"fieldtype\": \"Custom HTML\", \"options\": \"<!-- <PERSON><PERSON> and <PERSON><PERSON> -->\\n    <div style=\\\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; padding: 0 6px;\\\">\\n        <div>\\n            <div style=\\\"font-size: 15px; margin-top: 48px;\\\"><strong>PURCHASE RECEIPT</strong></div>\\n        </div>\\n        <div class='text-right'><img src=\\\"https://erp.rubis.co.tz/files/RTS%20Logo%20Excel.png\\\" style=\\\"height: 60px;\\\"></div>\\n    </div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<style>\\n    .address-block {\\n        line-height: 1.4;\\n    }\\n    /* ---------- vendor table ---------- */\\n    .vendor-table td { padding: 2px 0; font-size: 11px; }\\n</style>\\n\\n{% set supplier = frappe.get_doc('Supplier', doc.supplier) %}\\n{% set company = frappe.get_doc('Company', doc.company) %}\\n{% set addresses = frappe.get_all(\\\"Address\\\", \\n    filters={ \\\"link_doctype\\\": \\\"Supplier\\\", \\\"link_name\\\": doc.supplier },\\n    fields=[\\\"address_line1\\\", \\\"address_line2\\\", \\\"city\\\", \\\"country\\\"]\\n) %}\\n\\n        <!-- \\u25b8 Vendor details -->\\n        <table class=\\\"vendor-table\\\" style=\\\"width:100%; margin-top:10px;\\\">\\n            <tr>\\n                <td>\\n                    <b>Supplier Details</b>\\n                </td>\\n                <td style='padding:5px 12rem 5px 2px !important;'></td>\\n                <td style=\\\"text-align:left; padding-left: 96px !important; font-weight: bold;\\\">PR NO</td>\\n                <td style=\\\"text-align:right; font-weight: bold;\\\">{{ doc.name or '' }}</td>\\n            </tr>\\n            \\n            <tr>\\n                <td>{{ doc.supplier_name }}</td>\\n                <td style='padding:5px 12rem 5px 2px !important;'></td>\\n                <td style=\\\"text-align:left; padding-left: 96px !important;\\\">PR Date</td>\\n                <td style=\\\"text-align:right;\\\">{{ frappe.utils.formatdate(doc.posting_date, \\\"dd-MMM-YYYY\\\") }}</td>\\n            </tr>\\n            \\n            <tr>\\n                <td>\\n                    {{ doc.address_display or '' }}\\n                </td>\\n                <td style='padding:5px 12rem 5px 2px !important;'></td>\\n                \\n            \\n            <tr>\\n                <td>TIN NO: <span>{{ supplier.tax_id or '' }}</span></td>\\n                <td style='padding:5px 12rem 5px 2px !important;'></td>\\n                <td style=\\\"text-align:left; padding-left: 96px !important;\\\">TIN NO</td>\\n                <td style=\\\"text-align:right;\\\">{{ company.tax_id or '' }}</td>\\n            </tr>\\n            \\n            <tr>\\n                <td>VRN NO: <span>{{ supplier.vrn or '' }}</span></td>\\n                <td style='padding:5px 12rem 5px 2px !important;'></td>\\n                <td style=\\\"text-align:left; padding-left: 96px !important;\\\">VRN NO</td>\\n                <td style=\\\"text-align:right;\\\">{{ company.vrn or '' }}</td>\\n            </tr>\\n        </table>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<style>\\n    .main-tables {\\n        width: 100%;\\n        border-collapse: collapse;\\n        border: 1px solid #000;\\n        margin-top: 10px;\\n        font-size: 11px;\\n    }\\n    .main-tables th {\\n        border: 1px solid #000;\\n        white-space: nowrap;\\n        text-align: center;\\n    }\\n    .main-tables td {\\n        border: 1px solid #000;\\n        text-align: center;\\n    }\\n</style>\\n\\n<table class='main-tables'>\\n    <thead>\\n        <tr>\\n            <th>Supplier NO</th>\\n            <th>Supplier Ref</th>\\n            <th>Due Date</th>\\n            <th>Project NO</th>\\n            <th>Currency</th>\\n        </tr>\\n    </thead>\\n    <tbody>\\n        <tr>\\n            <td>{{ doc.supplier or '' }}</td>\\n            <td>{{ doc.bill_no or '' }}</td>\\n            <td>{{ frappe.utils.formatdate(doc.due_date, \\\"dd-MMM-YYYY\\\") }}</td>\\n            <td>{{ doc.project or '' }}</td>\\n            \\n            <td>{{ doc.currency }}</td>\\n        </tr>\\n    </tbody>\\n</table>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<!-- ===================== PURCHASE ORDER PRINT FORMAT ===================== -->\\n<style>\\n    /* ---------- fonts ---------- */\\n    @font-face {\\n        font-family: \\\"Calibri\\\";\\n        src: local(\\\"Calibri\\\"), local(\\\"Calibri-Regular\\\");\\n    }\\n\\n    /* ---------- global defaults ---------- */\\n    body       { font-family: \\\"Calibri\\\", sans-serif; font-size: 11px; color: #000; }\\n\\n    /* ---------- scaffold ---------- */\\n    .print-page {\\n        position: relative;\\n        min-height: 39vh;          \\n        box-sizing: border-box;\\n    }\\n    /* ---------- items table ---------- */\\n    .main-table {\\n        width: 100%;\\n        border-collapse: collapse;\\n        margin-top: 20px;\\n        font-size: 11px;\\n    }\\n    .main-table th {\\n        border-top: 1px solid #000;\\n        border-bottom: 1px solid #000;\\n        white-space: nowrap;\\n    }\\n\\n    @media print { @page { margin: 12mm 10mm; } }\\n</style>\\n\\n<div class=\\\"print-page\\\">\\n\\n    <!-- ======== VENDOR + ITEM LIST ======== -->\\n    <div class=\\\"content-block\\\">\\n        \\n\\n        <!-- \\u25b8 Items table -->\\n        <table class=\\\"main-table\\\">\\n            <thead>\\n                <tr>\\n                    <th><strong>Item Code</strong></th>\\n                    <th class='text-left'><strong>Description</strong></th>\\n                    <th class='text-center'><strong>Qty</strong></th>\\n                    <th class='text-center'><strong>Unit</strong></th>\\n                    <th class='text-right'><strong>Unit Price</strong></th>\\n                    <th class='text-right'><strong>Tax</strong></th>\\n                    <th class='text-right'><strong>Total</strong></th>\\n                </tr>\\n            </thead>\\n            <tbody>\\n                {% for item in doc.items %}\\n                    \\n                    \\n                    {% set tax_details = frappe.get_all(\\n                        \\\"Item Tax Template Detail\\\",\\n                        filters={\\\"parent\\\": item.item_tax_template},\\n                        fields=[\\\"tax_rate\\\"],\\n                        limit=1\\n                    ) %}\\n                    {% set tax_rate = tax_details[0].tax_rate if tax_details else 0 %}\\n\\n                    <tr>\\n                        <td>{{ item.item_code }}</td>\\n                        <td>{{ item.item_name }}</td>\\n                        <td class='text-center'>{{ item.qty | int }}</td>\\n                        <td class='text-center'>{{ item.uom }}</td>\\n                        <td class='text-right'>\\n                            {{ frappe.format(item.rate, \\\"Float\\\") }}\\n                        </td>\\n                        <td class='text-right'>\\n                            {{ frappe.format(item.amount * (tax_rate/100), \\\"Float\\\") }}\\n                        </td>\\n                        <td class='text-right'>\\n                            {{ frappe.format(item.amount, \\\"Float\\\") }}\\n                        </td>\\n                    </tr>\\n                {% endfor %}\\n            </tbody>\\n        </table>\\n    </div>\\n</div>\\n\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<style>\\n    .terms-section   { flex: 0 0 28%; font-size: 11px; }\\n    .section-title { font-weight: bold; margin-top: 0; }\\n    \\n</style>\\n\\n<div class='d-2'>\\n    <div class=\\\"terms-section\\\">\\n        <div class=\\\"section-title\\\">Terms and Conditions</div>\\n        <p>{{ doc.terms or '' }}</p>\\n        <p class='mt-3'>{{ doc.tc_name or '' }}</p>\\n    </div>\\n</div>\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<style>\\n    .d-2 {\\n        position: relative;\\n        top: 100;\\n    }\\n    .totals-table {\\n        width: 100%;                      /* fills right-hand column */\\n        border-collapse: collapse;\\n        font-size: 11px;\\n    }\\n    .totals-table td {\\n        padding: 2px 6px;\\n        text-align: right;\\n    }\\n    .totals-table tr td:first-child { font-weight: bold; text-align: left; }\\n</style>\\n\\n<div class='d-2 mb-3' style='padding-left: 145px !important;'>\\n    <table class=\\\"totals-table\\\">\\n        <tr><td>Subtotal:</td>\\n            <td>{{ frappe.format(doc.net_total, \\\"Float\\\") }}</td></tr>\\n        <tr><td>VAT 18%</td>\\n            <td>{{ frappe.format(doc.total_taxes_and_charges, \\\"Float\\\") }}</td></tr>\\n        <tr><td>Total:</td>\\n            <td>{{ frappe.format(doc.grand_total, \\\"Float\\\") }}</td></tr>\\n    </table>\\n</div>\"}, {\"fieldtype\": \"Section Break\", \"label\": \"\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"\\n<div class='d-2' style='font-size: 11px; margin-top: 2px'>\\n    <div style='font-weight: bold;'>Delivery Details and Notes</div>\\n    <p>{{doc.custom_delivery_details_and_notes or ''}}</p>\\n</div>\"}, {\"fieldtype\": \"Column Break\"}, {\"fieldname\": \"_custom_html\", \"print_hide\": 0, \"label\": \"Custom HTML\", \"fieldtype\": \"HTML\", \"options\": \"<div class=\\\"values-section d-2\\\" style='padding-left: 145px !important;'>\\n    <div class=\\\"section-title text-center mb-2\\\">Values in TZS for Tax Purpose Only</div>\\n    \\n    <table style=\\\"width:100%; font-size: 11px;\\\">\\n        <tr><td>Net Amount</td>\\n            <td style=\\\"text-align:right\\\">\\n                {{ frappe.format(doc.base_net_total, 'Currency') }}</td></tr>\\n        <tr><td>VAT 18%</td>\\n            <td style=\\\"text-align:right\\\">\\n                {{ frappe.format(doc.base_total_taxes_and_charges, 'Currency') }}</td></tr>\\n        <tr><td>Grand Total</td>\\n            <td style=\\\"text-align:right\\\">\\n                {{ frappe.format(doc.base_grand_total, 'Currency') }}</td></tr>\\n    </table>\\n</div>\"}]", "idx": 0, "line_breaks": 0, "margin_bottom": 0.0, "margin_left": 0.0, "margin_right": 0.0, "margin_top": 0.0, "modified": "2025-08-14 09:30:08.262029", "modified_by": "<EMAIL>", "module": "CSF TZ", "name": "Rubis Purchase Receipt", "owner": "<EMAIL>", "page_number": "<PERSON>de", "pdf_generator": "chrome", "print_designer": 0, "print_designer_template_app": "print_designer", "print_format_builder": 0, "print_format_builder_beta": 0, "print_format_type": "<PERSON><PERSON>", "raw_printing": 0, "show_section_headings": 0, "standard": "Yes"}