#!/usr/bin/env python3

import frappe
import sys
import os

# Add the frappe-bench directory to the path
sys.path.insert(0, '/home/<USER>/Desktop/frappe-bench')

def update_print_formats():
    """Update all Rubis print formats with optimized CSS"""
    
    # Optimized CSS for all print formats
    optimized_css = """/* ===================== GLOBAL PRINT STYLES ===================== */
.print-format .letter-head {
    display: none;
}

/* Reset default spacing and alignment */
.print-format {
    padding: 15px !important;
    font-family: 'Cal<PERSON>ri', Arial, sans-serif;
    font-size: 11px;
    line-height: 1.3;
    color: #000;
    max-width: 100%;
}

.print-format td, .print-format th {
    vertical-align: top !important;
    padding: 3px 5px !important;
}

.print-format th {
    color: black !important;
    font-weight: bold;
    border-bottom-width: 1px !important;
}

.print-format p {
    margin: 0px 0px 4px !important;
}

/* Remove default ERPNext spacing */
.frappe-control, .form-section {
    margin-bottom: 0 !important;
}

/* ===================== RESPONSIVE LAYOUT ===================== */
html, body {
    height: auto !important;
    overflow: visible !important;
}

.print-format {
    display: block;
    height: auto;
    overflow: visible;
}

/* ===================== PAGE BREAK HANDLING ===================== */
@media print {
    @page {
        margin: 8mm 6mm;
        size: A4;
    }
    
    .print-format {
        padding: 6px !important;
        font-size: 10px;
    }
    
    /* Prevent page breaks inside important sections */
    .header-section, .customer-details, .supplier-details, .order-details {
        page-break-inside: avoid;
    }
    
    /* Allow page breaks between item rows if needed */
    .items-table tbody tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }
    
    /* Keep footer sections together */
    .footer-section {
        page-break-inside: avoid;
        margin-top: 10px;
    }
    
    /* Optimize spacing for print */
    .responsive-margin {
        margin-top: 8px !important;
    }
}

/* ===================== DYNAMIC CONTENT HANDLING ===================== */
.content-wrapper {
    display: flex;
    flex-direction: column;
    min-height: auto;
}

.items-section {
    flex: 1;
    margin: 12px 0;
}

.footer-section {
    margin-top: auto;
    padding-top: 12px;
}

/* ===================== ALIGNMENT FIXES ===================== */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

/* ===================== TABLE IMPROVEMENTS ===================== */
.items-table {
    width: 100%;
    border-collapse: collapse;
    margin: 12px 0;
    font-size: 11px;
}

.items-table th {
    border-top: 1px solid #000;
    border-bottom: 1px solid #000;
    padding: 4px 6px;
    font-weight: bold;
    background-color: #f8f9fa;
}

.items-table td {
    border-bottom: 1px solid #ddd;
    padding: 3px 6px;
}

/* Handle many items by reducing row height */
.items-table.many-items {
    font-size: 10px;
}

.items-table.many-items td,
.items-table.many-items th {
    padding: 2px 4px;
    line-height: 1.1;
}

/* ===================== RESPONSIVE SPACING ===================== */
.responsive-margin {
    margin-top: 12px;
}

@media print {
    .responsive-margin {
        margin-top: 6px;
    }
}

/* ===================== FOOTER LAYOUT ===================== */
.footer-layout {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-top: 15px;
    gap: 20px;
}

.footer-left {
    flex: 1;
    max-width: 45%;
}

.footer-right {
    flex: 1;
    max-width: 45%;
}

@media print {
    .footer-layout {
        margin-top: 8px;
        gap: 10px;
    }
}

"""

    # List of print formats to update
    print_formats = [
        "Rubis Sales Order",
        "Rubis Purchase Order", 
        "Rubis Purchase Receipt"
    ]
    
    # Initialize Frappe
    frappe.init(site='rubis')
    frappe.connect()
    
    try:
        for format_name in print_formats:
            try:
                print(f"Updating {format_name}...")
                doc = frappe.get_doc("Print Format", format_name)
                doc.css = optimized_css
                doc.font_size = 11
                doc.save()
                print(f"✅ {format_name} updated successfully!")
            except Exception as e:
                print(f"❌ Error updating {format_name}: {str(e)}")
        
        # Commit all changes
        frappe.db.commit()
        print("\n🎉 All print formats updated successfully!")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    finally:
        frappe.destroy()

if __name__ == "__main__":
    update_print_formats()
