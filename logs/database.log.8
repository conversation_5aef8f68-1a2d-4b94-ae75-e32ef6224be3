2025-07-27 12:58:58,978 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-07-27 14:07:44,169 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 14:07:45,204 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 14:07:46,729 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 14:07:48,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 14:07:48,873 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-27 14:07:50,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 14:07:50,392 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-27 14:07:56,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0
2025-07-27 14:08:17,077 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 14:08:17,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 14:08:19,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 14:08:20,638 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-27 14:08:20,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 14:08:22,258 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 14:08:22,528 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 14:08:27,015 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0
2025-07-27 14:09:17,234 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 14:09:18,007 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 14:09:19,046 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 14:09:20,666 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-27 14:09:20,933 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-27 14:09:22,082 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-27 14:09:22,311 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 14:09:26,882 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-07-27 14:09:59,067 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 14:09:59,829 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 14:10:00,836 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 14:10:02,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-07-27 14:10:02,652 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 14:10:02,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-27 14:10:04,056 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 14:10:04,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 14:10:09,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0
2025-07-27 14:10:34,852 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 14:10:35,758 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 14:10:37,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 14:10:38,810 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-07-27 14:10:39,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 14:10:39,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 14:10:41,058 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 14:10:41,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 14:10:46,860 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0
2025-07-27 14:13:27,979 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `final` int(1) not null default 0
2025-07-27 14:13:27,979 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `final` int(1) not null default 0
2025-07-27 14:13:27,979 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `final` int(1) not null default 0
2025-07-27 14:13:27,979 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `final` int(1) not null default 0
2025-07-27 14:14:30,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `eir_number_reference` varchar(140)
2025-07-27 14:14:30,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `eir_number_reference` varchar(140)
2025-07-27 14:14:30,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `eir_number_reference` varchar(140)
2025-07-27 14:14:30,674 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `eir_number_reference` varchar(140)
2025-07-27 14:18:35,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `consignee_name` varchar(140)
2025-07-27 14:18:35,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `consignee_name` varchar(140)
2025-07-27 14:18:35,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `consignee_name` varchar(140)
2025-07-27 14:18:35,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `consignee_name` varchar(140)
2025-07-27 14:21:20,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `posting_date` date
2025-07-27 14:21:20,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `posting_date` date
2025-07-27 14:21:20,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `posting_date` date
2025-07-27 14:21:20,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Interchange` ADD COLUMN `posting_date` date
2025-07-27 14:57:34,354 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 14:57:35,236 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 14:57:36,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 14:57:38,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-27 14:57:38,655 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-27 14:57:39,780 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-27 14:57:39,966 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-27 14:57:44,447 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-07-27 15:07:55,420 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 15:07:56,203 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 15:07:57,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 15:07:59,073 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-27 15:07:59,350 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-27 15:08:00,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-27 15:08:00,749 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 15:08:05,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0
2025-07-27 15:23:05,621 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-27 15:23:06,497 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-27 15:23:07,741 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-27 15:23:09,432 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-27 15:23:09,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-27 15:23:10,835 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-27 15:23:11,040 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-27 15:23:15,910 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-07-28 08:58:46,000 WARNING database DDL Query made to DB:
create table `tabCargo Line` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-28 08:59:03,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Line` ADD COLUMN `cargo` varchar(140)
2025-07-28 08:59:03,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Line` ADD COLUMN `cargo` varchar(140)
2025-07-28 08:59:03,051 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo Line` ADD COLUMN `cargo` varchar(140)
2025-07-31 12:06:58,563 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-31 12:07:01,092 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-31 12:07:04,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0
2025-07-31 12:07:05,525 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-31 12:07:06,139 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation` MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `hour_rate_electricity` decimal(21,9) not null default 0, MODIFY `hour_rate_consumable` decimal(21,9) not null default 0, MODIFY `hour_rate_labour` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `hour_rate_rent` decimal(21,9) not null default 0
2025-07-31 12:07:06,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Scrap Item` MODIFY `stock_qty` decimal(21,9) not null default 0
2025-07-31 12:07:07,067 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `for_qty` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0
2025-07-31 12:07:07,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `per_ordered` decimal(21,9) not null default 0
2025-07-31 12:07:08,600 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-07-31 12:07:10,114 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-31 12:07:11,416 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-31 12:07:12,407 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0
2025-07-31 12:07:12,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD COLUMN `do_not_include_in_accounts` int(1) not null default 0
2025-07-31 12:07:12,809 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` MODIFY `additional_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `default_amount` decimal(21,9) not null default 0, MODIFY `tax_on_additional_salary` decimal(21,9) not null default 0, MODIFY `tax_on_flexible_benefit` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0
2025-07-31 12:07:13,064 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` MODIFY `total_working_days_per_year` decimal(21,9) not null default 365.0
2025-07-31 12:07:13,373 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `do_not_include_in_accounts` int(1) not null default 0
2025-07-31 12:07:13,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-07-31 12:07:15,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-31 12:07:16,029 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-31 12:07:17,030 WARNING database DDL Query made to DB:
create table `tabOpenAI Query Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`doctype_name` varchar(140),
`query` longtext,
`response` longtext,
`is_cached` int(1) not null default 0,
`resend_count` int(11) not null default 0,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 12:07:17,707 WARNING database DDL Query made to DB:
create table `tabFeedback Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140),
`is_active` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 12:07:18,422 WARNING database DDL Query made to DB:
create table `tabFeedback Form` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140),
`participant_name` varchar(140),
`designation` varchar(140),
`contact_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 12:07:18,643 WARNING database DDL Query made to DB:
create table `tabFeedback Question` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` text,
`fieldtype` varchar(140),
`options` text,
`reqd` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 12:07:18,844 WARNING database DDL Query made to DB:
create table `tabFeedback Response` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` text,
`answer` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-07-31 12:07:27,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-07-31 13:00:51,827 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-07-31 13:00:52,899 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-07-31 13:00:54,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-07-31 13:00:55,394 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-07-31 13:00:55,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-07-31 13:00:56,905 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-07-31 13:00:57,112 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-07-31 13:01:00,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0
2025-08-02 14:23:42,776 WARNING database DDL Query made to DB:
create table `tabConsignee Disbursement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-02 15:35:04,432 WARNING database DDL Query made to DB:
alter table `tabConsignee Disbursement` add column if not exists parent varchar(140)
2025-08-02 15:35:04,432 WARNING database DDL Query made to DB:
alter table `tabConsignee Disbursement` add column if not exists parenttype varchar(140)
2025-08-02 15:35:04,433 WARNING database DDL Query made to DB:
alter table `tabConsignee Disbursement` add column if not exists parentfield varchar(140)
2025-08-02 15:35:04,506 WARNING database DDL Query made to DB:
ALTER TABLE `tabConsignee Disbursement` ADD COLUMN `payment_entry` varchar(140), ADD COLUMN `paid_to` varchar(140), ADD COLUMN `purpose` varchar(140), ADD COLUMN `heading` varchar(140)
2025-08-02 15:38:37,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0
2025-08-02 15:40:03,936 WARNING database DDL Query made to DB:
alter table `tabConsignee Disbursement` add column if not exists parent varchar(140)
2025-08-02 15:40:03,937 WARNING database DDL Query made to DB:
alter table `tabConsignee Disbursement` add column if not exists parenttype varchar(140)
2025-08-02 15:40:03,938 WARNING database DDL Query made to DB:
alter table `tabConsignee Disbursement` add column if not exists parentfield varchar(140)
2025-08-02 15:42:47,550 WARNING database DDL Query made to DB:
alter table `tabConsignee Disbursement` add column if not exists parent varchar(140)
2025-08-02 15:42:47,552 WARNING database DDL Query made to DB:
alter table `tabConsignee Disbursement` add column if not exists parenttype varchar(140)
2025-08-02 15:42:47,552 WARNING database DDL Query made to DB:
alter table `tabConsignee Disbursement` add column if not exists parentfield varchar(140)
2025-08-02 17:39:47,580 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-02 17:39:48,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-02 17:39:49,235 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` ADD COLUMN `advance_voucher_type` varchar(140), ADD COLUMN `advance_voucher_no` varchar(140)
2025-08-02 17:39:49,259 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `payment_term_outstanding` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-08-02 17:39:49,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Ledger Entry` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `amount_in_account_currency` decimal(21,9) not null default 0
2025-08-02 17:39:50,148 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` ADD COLUMN `advance_voucher_type` varchar(140), ADD COLUMN `advance_voucher_no` varchar(140)
2025-08-02 17:39:50,174 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` MODIFY `debit_in_account_currency` decimal(21,9) not null default 0, MODIFY `credit_in_account_currency` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `debit` decimal(21,9) not null default 0, MODIFY `credit` decimal(21,9) not null default 0
2025-08-02 17:39:50,363 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` ADD COLUMN `delinked` int(1) not null default 0
2025-08-02 17:39:50,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` MODIFY `amount` decimal(21,9) not null default 0
2025-08-02 17:39:50,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` ADD INDEX `creation`(`creation`)
2025-08-02 17:39:51,496 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0
2025-08-02 17:39:51,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-02 17:39:52,176 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkstation` MODIFY `hour_rate_electricity` decimal(21,9) not null default 0, MODIFY `hour_rate_consumable` decimal(21,9) not null default 0, MODIFY `hour_rate_rent` decimal(21,9) not null default 0, MODIFY `hour_rate_labour` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0
2025-08-02 17:39:52,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Card Scrap Item` MODIFY `stock_qty` decimal(21,9) not null default 0
2025-08-02 17:39:52,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabPick List` MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `for_qty` decimal(21,9) not null default 0
2025-08-02 17:39:53,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request` MODIFY `per_ordered` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-08-02 17:39:53,401 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset Repair` MODIFY `total_repair_cost` decimal(21,9) not null default 0
2025-08-02 17:39:54,239 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing Charges` MODIFY `total_charges_sum` decimal(21,9) not null default 0, MODIFY `shipment_clearance_total` decimal(21,9) not null default 0, MODIFY `tra_clearance_total` decimal(21,9) not null default 0, MODIFY `port_clearance_total` decimal(21,9) not null default 0, MODIFY `physical_clearance_total` decimal(21,9) not null default 0
2025-08-02 17:39:54,883 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-02 17:39:55,108 WARNING database DDL Query made to DB:
ALTER TABLE `tabJob Offer` ADD INDEX `creation`(`creation`)
2025-08-02 17:39:55,606 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-02 17:39:55,884 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity` ADD INDEX `creation`(`creation`)
2025-08-02 17:39:56,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0
2025-08-02 17:39:56,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD COLUMN `do_not_include_in_accounts` int(1) not null default 0
2025-08-02 17:39:56,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` MODIFY `tax_on_flexible_benefit` decimal(21,9) not null default 0, MODIFY `tax_on_additional_salary` decimal(21,9) not null default 0, MODIFY `default_amount` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `additional_amount` decimal(21,9) not null default 0
2025-08-02 17:39:56,461 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Detail` ADD INDEX `creation`(`creation`)
2025-08-02 17:39:56,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` MODIFY `total_working_days_per_year` decimal(21,9) not null default 365.0
2025-08-02 17:39:56,723 WARNING database DDL Query made to DB:
ALTER TABLE `tabGratuity Rule` ADD INDEX `creation`(`creation`)
2025-08-02 17:39:56,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `do_not_include_in_accounts` int(1) not null default 0
2025-08-02 17:39:56,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `max_benefit_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `hourly_rate` decimal(21,9) not null default 0
2025-08-02 17:39:58,178 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-08-02 17:39:58,381 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-02 17:39:58,811 WARNING database DDL Query made to DB:
create table `tabFeedback Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140),
`is_active` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-02 17:39:59,071 WARNING database DDL Query made to DB:
create table `tabFeedback Form` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template` varchar(140),
`participant_name` varchar(140),
`designation` varchar(140),
`contact_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-02 17:39:59,224 WARNING database DDL Query made to DB:
create table `tabFeedback Question` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` text,
`fieldtype` varchar(140),
`options` text,
`reqd` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-02 17:39:59,320 WARNING database DDL Query made to DB:
create table `tabFeedback Response` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` text,
`answer` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-02 17:39:59,683 WARNING database DDL Query made to DB:
truncate `tabAdvance Payment Ledger Entry`
2025-08-02 17:40:04,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-08-03 18:23:00,358 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-03 18:23:01,510 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-03 18:23:01,916 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` ADD COLUMN `advance_voucher_type` varchar(140), ADD COLUMN `advance_voucher_no` varchar(140)
2025-08-03 18:23:01,938 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` MODIFY `allocated_amount` decimal(21,9) not null default 0, MODIFY `payment_term_outstanding` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-08-03 18:23:02,251 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Ledger Entry` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `amount_in_account_currency` decimal(21,9) not null default 0
2025-08-03 18:23:02,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` ADD COLUMN `advance_voucher_type` varchar(140), ADD COLUMN `advance_voucher_no` varchar(140)
2025-08-03 18:23:02,800 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry Account` MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `credit_in_account_currency` decimal(21,9) not null default 0, MODIFY `credit` decimal(21,9) not null default 0, MODIFY `debit` decimal(21,9) not null default 0, MODIFY `debit_in_account_currency` decimal(21,9) not null default 0
2025-08-03 18:23:02,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` ADD COLUMN `delinked` int(1) not null default 0
2025-08-03 18:23:02,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` MODIFY `amount` decimal(21,9) not null default 0
2025-08-03 18:23:03,039 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdvance Payment Ledger Entry` ADD INDEX `creation`(`creation`)
2025-08-03 18:23:03,766 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-03 18:23:05,141 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-03 18:23:05,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-03 18:23:06,864 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-03 18:23:07,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-03 18:23:07,734 WARNING database DDL Query made to DB:
truncate `tabAdvance Payment Ledger Entry`
2025-08-03 18:23:11,730 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0
2025-08-04 09:15:25,090 WARNING database DDL Query made to DB:
create table `tabDrive Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140) unique,
`default_team` varchar(140),
`single_click` int(1) not null default 0,
`auto_detect_links` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:25,326 WARNING database DDL Query made to DB:
create table `tabDrive Document Version` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`snapshot_message` text,
`parent_entity` varchar(140),
`parent_document` varchar(140),
`snapshot_data` longtext,
`snapshot_size` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:25,419 WARNING database DDL Query made to DB:
create sequence if not exists drive_favourite_id_seq nocache nocycle
2025-08-04 09:15:25,440 WARNING database DDL Query made to DB:
create table `tabDrive Favourite` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`entity` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:25,544 WARNING database DDL Query made to DB:
create table `tabDrive Entity Tag` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`tag` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:25,699 WARNING database DDL Query made to DB:
create sequence if not exists drive_entity_log_id_seq nocache nocycle
2025-08-04 09:15:25,710 WARNING database DDL Query made to DB:
create table `tabDrive Entity Log` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`entity_name` varchar(140),
`last_interaction` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:25,809 WARNING database DDL Query made to DB:
create table `tabDrive Entity Activity Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`entity` varchar(140),
`document_field` varchar(140),
`action_type` varchar(140),
`old_value` varchar(140),
`new_value` varchar(140),
`message` varchar(140),
`meta_value` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=MyISAM
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:25,908 WARNING database DDL Query made to DB:
create table `tabDrive Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`content` longtext default 'AAA=',
`settings` json default '{\n   \"docWidth\": false,\n   \"docSize\": true,\n   \"docFont\": \"font-fd-sans\",\n   \"docHeader\": false\n}',
`comments2` json,
`raw_content` longtext,
`mentions` json,
`version` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:25,986 WARNING database DDL Query made to DB:
create table `tabDrive Team Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`access_level` int(11) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:26,166 WARNING database DDL Query made to DB:
create table `tabAccount Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_key` varchar(140),
`email` varchar(140),
`login_count` int(11) not null default 0,
`signed_up` int(1) not null default 0,
`otp` varchar(140),
`otp_generated_at` datetime(6),
`invite` varchar(140),
`oauth_signup` int(1) not null default 0,
`ip_address` varchar(140),
`referral_source` varchar(140),
`url_args` longtext,
`referrer_id` varchar(140),
`geo_location` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `email`(`email`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:26,279 WARNING database DDL Query made to DB:
create table `tabDrive File` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`team` varchar(140),
`is_group` int(1) not null default 0,
`is_link` int(1) not null default 0,
`parent_entity` varchar(140),
`path` text,
`color` varchar(140),
`mime_type` varchar(140),
`file_size` bigint(20) not null default 0,
`is_active` int(11) not null default 1,
`document` varchar(140),
`is_private` int(1) not null default 0,
`old_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:26,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrive File`
				ADD INDEX IF NOT EXISTS `title_index`(title)
2025-08-04 09:15:26,675 WARNING database DDL Query made to DB:
create table `tabDrive Notification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`from_user` varchar(140),
`to_user` varchar(140),
`read` int(1) not null default 0,
`type` varchar(140),
`message` text,
`notif_doctype` varchar(140),
`notif_doctype_name` varchar(140),
`entity_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:26,763 WARNING database DDL Query made to DB:
create table `tabDrive Tag` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:26,846 WARNING database DDL Query made to DB:
create table `tabDrive Comment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`content` longtext,
`resolved` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:26,940 WARNING database DDL Query made to DB:
create table `tabDrive Team` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` text,
`storage` int(11) not null default 5120,
`quota` int(11) not null default 5120,
`team_domain` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:27,052 WARNING database DDL Query made to DB:
create table `tabDrive Permission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`entity` varchar(140),
`user` varchar(140),
`read` int(1) not null default 0,
`comment` int(1) not null default 0,
`share` int(1) not null default 0,
`write` int(1) not null default 0,
`upload` int(1) not null default 0,
`valid_until` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:27,151 WARNING database DDL Query made to DB:
create table `tabDrive User Invitation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`email` varchar(140),
`team` varchar(140),
`status` varchar(140),
`accepted_on` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-08-04 09:15:28,097 WARNING database DDL Query made to DB:
ALTER TABLE `tabDrive File` ADD FULLTEXT INDEX drive_file_title_fts_idx (title)
2025-08-04 09:16:11,137 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-04 09:16:12,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-04 09:16:14,118 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-04 09:16:16,231 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-08-04 09:16:16,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-04 09:16:18,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-04 09:16:18,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-04 09:16:24,605 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0
2025-08-04 09:16:38,995 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-04 09:16:39,934 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-04 09:16:40,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-04 09:16:41,988 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-08-04 09:16:42,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-08-04 09:16:43,461 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-04 09:16:43,688 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-04 09:16:47,279 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0
2025-08-04 10:32:18,781 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-04 10:32:19,763 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-04 10:32:21,040 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-04 10:32:22,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-04 10:32:22,693 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-04 10:32:24,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-08-04 10:32:24,377 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-08-04 10:32:28,946 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-08-04 11:09:36,573 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-04 11:09:37,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-04 11:09:38,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-04 11:09:39,595 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-08-04 11:09:39,848 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-04 11:09:40,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-08-04 11:09:41,080 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-08-04 11:09:44,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0
2025-08-04 14:20:50,116 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-04 14:20:51,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-04 14:20:52,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-04 14:20:54,218 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-08-04 14:20:54,565 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-04 14:20:55,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-04 14:20:56,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-04 14:21:00,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0
2025-08-05 08:59:49,671 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-08-05 08:59:49,823 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-08-05 08:59:49,940 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-08-05 17:50:22,435 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-05 17:50:23,702 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-05 17:50:25,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-05 17:50:27,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-05 17:50:28,025 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-08-05 17:50:29,654 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-05 17:50:29,891 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-08-05 17:50:34,629 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0
2025-08-05 17:58:26,748 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-08-05 17:58:27,704 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-08-05 17:58:28,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-08-05 17:58:30,282 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-05 17:58:30,688 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-08-05 17:58:32,011 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-08-05 17:58:32,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
