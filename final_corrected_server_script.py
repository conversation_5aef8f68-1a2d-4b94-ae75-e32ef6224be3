# Get Basic Pay from Salary Structure Assignment
ssa = frappe.get_all(
    "Salary Structure Assignment",
    filters={"employee": doc.employee},
    fields=["base"],
    limit=1,
)
basic_pay = ssa[0].base if ssa else 0

# Get Working Hours per Month from CSF TZ Settings
working_hours_per_month = (
    frappe.db.get_single_value("CSF TZ Settings", "working_hours_per_month") or 0
)

# Calculate working days in the payroll month (excluding Sundays + Holidays)
month_start = frappe.utils.get_first_day(doc.payroll_date)
month_end = frappe.utils.get_last_day(doc.payroll_date)

total_days = (month_end - month_start).days + 1
sundays = 0
for i in range(total_days):
    day = frappe.utils.add_days(month_start, i)
    if day.weekday() == 6:  # Sunday
        sundays = sundays + 1

# Get Holiday List for Employee
from hrms.hr.utils import get_holiday_list_for_employee
holiday_list = get_holiday_list_for_employee(doc.employee, raise_exception=False)

# Get holidays from the employee's holiday list
holidays = []
if holiday_list:
    holidays = frappe.get_all(
        "Holiday",
        filters={
            "parent": holiday_list,
            "holiday_date": ["between", [month_start, month_end]]
        },
        fields=["holiday_date"],
    )

# Avoid double-counting holidays that fall on Sundays
holiday_dates = []
for h in holidays:
    if h.holiday_date.weekday() != 6:
        holiday_dates.append(h.holiday_date)

working_days = total_days - sundays - len(holiday_dates)
frappe.msgprint("Working Days: " + str(working_days))

# Calculate Gross Advance
gross_advance = (
    (basic_pay / working_days) * (doc.no_of_days or 0)
    + (basic_pay / working_hours_per_month) * 1.5 * (doc.normal_ot_hours or 0)
    + (basic_pay / working_hours_per_month) * 2 * (doc.holiday_ot_hours or 0)
    + (doc.production_incentive or 0)
)

# Calculate Amount
doc.amount = gross_advance * 0.6
